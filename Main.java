import java.util.*;

public class Main {
    public static void main(String[] args) {
        String input = "   5*X^0+4*X^1   -9.3*X^2=1*X^0   ";
        input = sanitize(input);

        Map<Integer, Double> coeffs = new HashMap<>();
        String[] sides = input.split("=");
        parseSide(sides[0], 1, coeffs);
        parseSide(sides[1], -1, coeffs);

        double c0 = coeffs.getOrDefault(0, 0.0);
        double c1 = coeffs.getOrDefault(1, 0.0);
        double c2 = coeffs.getOrDefault(2, 0.0);

        System.out.println("c0 = " + c0 + ", c1 = " + c1 + ", c2 = " + c2);

        if (c2 != 0) {
            double d = c1 * c1 - 4 * c2 * c0;
            if (d < 0) System.out.println("No real solution");
            else {
                double x1 = (-c1 - Math.sqrt(d)) / (2 * c2);
                double x2 = (-c1 + Math.sqrt(d)) / (2 * c2);
                System.out.println("x = " + x1 + " or " + x2);
            }
        } else if (c1 != 0) {
            System.out.println("x = " + (-c0 / c1));
        } else {
            System.out.println(c0 == 0 ? "Infinite solutions" : "No solution");
        }
    }

    // Simple sanitizing
    static String sanitize(String s) {
            s = s.trim();
            s = s.replaceAll("\\s+", " ");        // collapse spaces
            s = s.replace("*", " * ")
                    .replace("=", " = ");
            s = s.replaceAll("\\s+", " ").trim(); // clean again
            return s;

    }

    // Parse side of the equation
    static void parseSide(String side, int sign, Map<Integer, Double> coeffs) {
        String[] tokens = side.split(" ");
        for (int i = 0; i < tokens.length; i++) {
            if (tokens[i].matches("[+-]?\\d+(\\.\\d+)?")) {
                double coeff = Double.parseDouble(tokens[i]) * sign;
                if (i + 2 < tokens.length && tokens[i + 1].equals("*") && tokens[i + 2].startsWith("X^")) {
                    int exp = Integer.parseInt(tokens[i + 2].substring(2));
                    coeffs.put(exp, coeffs.getOrDefault(exp, 0.0) + coeff);
                }
            }
        }
    }
}

